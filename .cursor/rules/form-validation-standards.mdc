---
description:
globs:
alwaysApply: false
---
# Form Validation Standards

## Overview
This project requires strict validation standards to ensure data integrity and user experience consistency between frontend and backend.

## Frontend Validation (MANDATORY)

### Required Components
Always use shadcn-vue forms with vee-validate and Zod schemas:

```vue
<script setup lang="ts">
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { z } from 'zod'
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'

// Define validation schema
const formSchema = toTypedSchema(z.object({
  name: z.string().min(1, 'Name is required').max(255, 'Name too long'),
  email: z.string().email('Invalid email format').max(255, 'Email too long'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
}))

const { handleSubmit, errors, isSubmitting } = useForm({
  validationSchema: formSchema
})
</script>
```

### Form Structure Pattern
```vue
<template>
  <form @submit="onSubmit" class="space-y-6">
    <FormField v-slot="{ componentField }" name="fieldName">
      <FormItem>
        <FormLabel>Field Label</FormLabel>
        <FormControl>
          <Input v-bind="componentField" placeholder="Enter value" />
        </FormControl>
        <FormMessage />
      </FormItem>
    </FormField>
    <Button type="submit" :disabled="isSubmitting">
      {{ isSubmitting ? 'Processing...' : 'Submit' }}
    </Button>
  </form>
</template>
```

## Backend Validation (MANDATORY)

### Step 1: Model Validation Rules
Define validation rules in the Model using static methods:

```php
<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ExampleModel extends Model
{
    public static function validationRules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'status' => ['required', 'in:active,inactive'],
        ];
    }

    public static function validationMessages(): array
    {
        return [
            'name.required' => 'Name is required',
            'name.max' => 'Name is too long',
            'email.email' => 'Invalid email format',
            'email.unique' => 'Email already exists',
            'status.in' => 'Status must be active or inactive',
        ];
    }
}
```

### Step 2: Form Request Classes
Create Form Request classes that use model validation rules:

```php
<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Models\ExampleModel;
use Illuminate\Foundation\Http\FormRequest;

class StoreExampleRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->can('create_example');
    }

    public function rules(): array
    {
        return ExampleModel::validationRules();
    }

    public function messages(): array
    {
        return ExampleModel::validationMessages();
    }
}
```

### Step 3: Frontend-Backend Synchronization
Create shared validation constants in [resources/js/types/validation.ts](mdc:resources/js/types/validation.ts):

```typescript
export const ValidationRules = {
  user: {
    name: {
      minLength: 1,
      maxLength: 255,
    },
    email: {
      maxLength: 255,
    },
    password: {
      minLength: 8,
    },
  },
  example: {
    title: {
      minLength: 1,
      maxLength: 100,
    },
    description: {
      maxLength: 500,
    },
  },
} as const;
```

## Critical Rules

### Frontend Requirements
- **NEVER** skip form validation
- **ALWAYS** use FormField components for consistency
- **ALWAYS** handle loading states with `isSubmitting`
- **ALWAYS** provide clear error messages
- **ALWAYS** use Zod schemas for type safety

### Backend Requirements
- **ALWAYS** define validation rules in the Model
- **ALWAYS** use Form Request classes
- **ALWAYS** sync validation constants with frontend
- **ALWAYS** provide custom error messages
- **NEVER** skip backend validation even if frontend validates

### Error Handling Pattern
```vue
<script setup lang="ts">
const onSubmit = handleSubmit((values) => {
  router.post('/endpoint', values, {
    onSuccess: () => {
      toast.success('Operation completed successfully')
      // Handle success
    },
    onError: (errors) => {
      toast.error('Please check the form for errors')
      // Form errors are automatically handled by vee-validate
    }
  })
})
</script>
```

## Validation Checklist

### Before Implementing Forms
- [ ] Define Zod schema with proper validation rules
- [ ] Create corresponding Laravel validation rules in Model
- [ ] Set up Form Request class
- [ ] Add validation constants to [resources/js/types/validation.ts](mdc:resources/js/types/validation.ts)
- [ ] Test both frontend and backend validation
- [ ] Ensure error messages are user-friendly
- [ ] Verify loading states work correctly
- [ ] Test edge cases and error scenarios
