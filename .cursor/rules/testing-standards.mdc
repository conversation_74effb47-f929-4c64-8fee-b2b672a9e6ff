---
description:
globs:
alwaysApply: false
---
# Testing Standards

## Overview
This project requires comprehensive testing to ensure code quality, reliability, and maintainability. All tests are written using Pest PHP for backend testing.

## Required Test Types

### Feature Tests
Test complete user workflows and integration scenarios:

```php
<?php

use App\Models\User;

describe('User Management Feature', function () {
    beforeEach(function () {
        $this->user = User::factory()->create();
        $this->actingAs($this->user);
    });

    describe('user creation workflow', function () {
        it('allows authorized users to create new users', function () {
            $userData = [
                'name' => '<PERSON> Doe',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'password_confirmation' => 'password123',
                'status' => 'active',
            ];

            $response = $this->post(route('users.store'), $userData);

            $response->assertRedirect(route('users.index'));
            $response->assertSessionHas('success', 'User created successfully');

            $this->assertDatabaseHas('users', [
                'name' => '<PERSON>e',
                'email' => '<EMAIL>',
                'status' => 'active',
            ]);
        });

        it('validates required fields', function () {
            $response = $this->post(route('users.store'), []);

            $response->assertSessionHasErrors(['name', 'email', 'password']);
        });

        it('prevents duplicate email addresses', function () {
            User::factory()->create(['email' => '<EMAIL>']);

            $response = $this->post(route('users.store'), [
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'password_confirmation' => 'password123',
            ]);

            $response->assertSessionHasErrors(['email']);
        });
    });
});
```

### Unit Tests
Test individual functions and methods in isolation:

```php
<?php

use App\Models\User;
use App\Models\Role;

describe('User Model', function () {
    describe('validation rules', function () {
        it('returns correct validation rules', function () {
            $rules = User::validationRules();

            expect($rules)->toHaveKey('name')
                ->and($rules)->toHaveKey('email')
                ->and($rules)->toHaveKey('password')
                ->and($rules['name'])->toContain('required', 'string', 'max:255')
                ->and($rules['email'])->toContain('required', 'email', 'unique:users');
        });

        it('returns correct validation messages', function () {
            $messages = User::validationMessages();

            expect($messages)->toHaveKey('name.required')
                ->and($messages)->toHaveKey('email.email')
                ->and($messages['name.required'])->toBe('Name is required');
        });
    });

    describe('relationships', function () {
        it('belongs to many roles', function () {
            $user = User::factory()->create();
            $role = Role::factory()->create();

            $user->roles()->attach($role);

            expect($user->roles)->toHaveCount(1)
                ->and($user->roles->first())->toBeInstanceOf(Role::class);
        });
    });

    describe('scopes', function () {
        it('filters active users', function () {
            User::factory()->create(['status' => 'active']);
            User::factory()->create(['status' => 'inactive']);

            $activeUsers = User::active()->get();

            expect($activeUsers)->toHaveCount(1)
                ->and($activeUsers->first()->status)->toBe('active');
        });
    });
});
```

### API Tests
Test API endpoints and responses:

```php
<?php

use App\Models\User;

describe('Users API', function () {
    beforeEach(function () {
        $this->user = User::factory()->create();
        $this->actingAs($this->user);
    });

    describe('GET /api/users', function () {
        it('returns paginated users list', function () {
            User::factory()->count(25)->create();

            $response = $this->getJson('/api/users');

            $response->assertOk()
                ->assertJsonStructure([
                    'data' => ['*' => ['id', 'name', 'email', 'status']],
                    'meta' => ['current_page', 'per_page', 'total', 'last_page'],
                    'links' => ['first', 'last', 'prev', 'next'],
                ]);

            expect($response->json('data'))->toHaveCount(15); // Default per page
            expect($response->json('meta.total'))->toBe(26); // 25 + current user
        });

        it('filters users by search query', function () {
            User::factory()->create(['name' => 'John Doe']);
            User::factory()->create(['name' => 'Jane Smith']);

            $response = $this->getJson('/api/users?search=John');

            $response->assertOk();
            expect($response->json('data'))->toHaveCount(1)
                ->and($response->json('data.0.name'))->toBe('John Doe');
        });

        it('filters users by status', function () {
            User::factory()->create(['status' => 'active']);
            User::factory()->create(['status' => 'inactive']);

            $response = $this->getJson('/api/users?status=active');

            $response->assertOk();
            $activeUsers = collect($response->json('data'))->where('status', 'active');
            expect($activeUsers)->toHaveCount(2); // 1 created + current user
        });
    });

    describe('POST /api/users', function () {
        it('creates user with valid data', function () {
            $userData = [
                'name' => 'New User',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'status' => 'active',
            ];

            $response = $this->postJson('/api/users', $userData);

            $response->assertCreated()
                ->assertJsonFragment(['name' => 'New User', 'email' => '<EMAIL>']);

            $this->assertDatabaseHas('users', [
                'name' => 'New User',
                'email' => '<EMAIL>',
            ]);
        });

        it('returns validation errors for invalid data', function () {
            $response = $this->postJson('/api/users', []);

            $response->assertUnprocessable()
                ->assertJsonValidationErrors(['name', 'email', 'password']);
        });
    });

    describe('DELETE /api/users/bulk-delete', function () {
        it('deletes multiple users', function () {
            $users = User::factory()->count(3)->create();
            $userIds = $users->pluck('id')->toArray();

            $response = $this->deleteJson('/api/users/bulk-delete', [
                'ids' => $userIds
            ]);

            $response->assertOk()
                ->assertJson(['success_count' => 3, 'error_count' => 0]);

            foreach ($userIds as $id) {
                $this->assertDatabaseMissing('users', ['id' => $id]);
            }
        });
    });
});
```

### Component Tests (Frontend)
Test Vue component behavior:

```javascript
// tests/components/DataTable.test.js
import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import DataTable from '@/components/DataTable.vue'

describe('DataTable Component', function () {
    const mockData = [
        { id: 1, name: 'John Doe', email: '<EMAIL>' },
        { id: 2, name: 'Jane Smith', email: '<EMAIL>' }
    ]

    const mockColumns = [
        { accessorKey: 'name', header: 'Name' },
        { accessorKey: 'email', header: 'Email' }
    ]

    it('renders table with data', function () {
        const wrapper = mount(DataTable, {
            props: {
                data: mockData,
                columns: mockColumns
            }
        })

        expect(wrapper.text()).toContain('John Doe')
        expect(wrapper.text()).toContain('<EMAIL>')
    })

    it('shows loading state', function () {
        const wrapper = mount(DataTable, {
            props: {
                data: [],
                columns: mockColumns,
                loading: true
            }
        })

        expect(wrapper.find('.loading-spinner')).toBeTruthy()
    })

    it('emits row selection events', async function () {
        const wrapper = mount(DataTable, {
            props: {
                data: mockData,
                columns: mockColumns
            }
        })

        await wrapper.find('input[type="checkbox"]').trigger('change')

        expect(wrapper.emitted('update:row-selection')).toBeTruthy()
    })
})
```

## Test Organization Structure

### Test Files Location
```
tests/
├── Feature/
│   ├── UserManagementTest.php
│   ├── SpecializationManagementTest.php
│   └── AuthenticationTest.php
├── Unit/
│   ├── Models/
│   │   ├── UserTest.php
│   │   └── SpecializationTest.php
│   ├── Services/
│   │   └── UserServiceTest.php
│   └── Helpers/
│       └── ValidationHelperTest.php
└── Api/
    ├── UserApiTest.php
    └── SpecializationApiTest.php
```

### Test Data Management
Use factories for consistent test data:

```php
<?php

// database/factories/UserFactory.php
namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;

class UserFactory extends Factory
{
    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'password' => Hash::make('password'),
            'status' => $this->faker->randomElement(['active', 'inactive']),
            'email_verified_at' => now(),
        ];
    }

    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
        ]);
    }

    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'inactive',
        ]);
    }
}
```

## Test Coverage Requirements

### Controller Testing
Test all public methods and edge cases:

```php
<?php

describe('UserController', function () {
    beforeEach(function () {
        $this->user = User::factory()->create();
        $this->actingAs($this->user);
    });

    describe('index method', function () {
        it('displays users index page', function () {
            $response = $this->get(route('users.index'));

            $response->assertOk()
                ->assertInertia(fn($page) => $page->component('Users/Index'));
        });

        it('applies search filter', function () {
            User::factory()->create(['name' => 'Searchable User']);

            $response = $this->get(route('users.index', ['search' => 'Searchable']));

            $response->assertOk()
                ->assertInertia(fn($page) =>
                    $page->has('users.data')
                        ->where('users.data.0.name', 'Searchable User')
                );
        });
    });

    describe('store method', function () {
        it('creates user and redirects', function () {
            $userData = [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'password_confirmation' => 'password123',
            ];

            $response = $this->post(route('users.store'), $userData);

            $response->assertRedirect(route('users.index'))
                ->assertSessionHas('success');
        });
    });

    describe('authorization', function () {
        it('requires authentication', function () {
            Auth::logout();

            $response = $this->get(route('users.index'));

            $response->assertRedirect(route('login'));
        });

        it('requires proper permissions', function () {
            $unauthorizedUser = User::factory()->create();
            $this->actingAs($unauthorizedUser);

            $response = $this->get(route('users.index'));

            $response->assertForbidden();
        });
    });
});
```

### Model Testing
Test relationships, scopes, and custom methods:

```php
<?php

describe('Specialization Model', function () {
    describe('relationships', function () {
        it('belongs to a program', function () {
            $program = Program::factory()->create();
            $specialization = Specialization::factory()->create(['program_id' => $program->id]);

            expect($specialization->program)->toBeInstanceOf(Program::class)
                ->and($specialization->program->id)->toBe($program->id);
        });

        it('has many users', function () {
            $specialization = Specialization::factory()->create();
            $users = User::factory()->count(3)->create();

            $specialization->users()->attach($users);

            expect($specialization->users)->toHaveCount(3);
        });
    });

    describe('scopes', function () {
        it('filters by status', function () {
            Specialization::factory()->create(['status' => 'active']);
            Specialization::factory()->create(['status' => 'inactive']);

            $activeSpecializations = Specialization::active()->get();

            expect($activeSpecializations)->toHaveCount(1)
                ->and($activeSpecializations->first()->status)->toBe('active');
        });
    });

    describe('validation', function () {
        it('provides correct validation rules', function () {
            $rules = Specialization::validationRules();

            expect($rules)->toHaveKey('name')
                ->and($rules)->toHaveKey('code')
                ->and($rules)->toHaveKey('program_id');
        });
    });
});
```

## Testing Best Practices

### Setup and Teardown
```php
<?php

describe('Feature Test Suite', function () {
    beforeEach(function () {
        // Run before each test
        $this->user = User::factory()->create();
        $this->actingAs($this->user);
    });

    afterEach(function () {
        // Run after each test (if needed)
        Cache::flush();
    });
});
```

### Database Testing
```php
<?php

describe('Database Operations', function () {
    it('creates record with correct attributes', function () {
        $user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>'
        ]);

        $this->assertDatabaseHas('users', [
            'name' => 'Test User',
            'email' => '<EMAIL>'
        ]);

        expect($user->name)->toBe('Test User')
            ->and($user->email)->toBe('<EMAIL>');
    });

    it('soft deletes records', function () {
        $user = User::factory()->create();

        $user->delete();

        $this->assertSoftDeleted('users', ['id' => $user->id]);
    });
});
```

### Error Handling Tests
```php
<?php

describe('Error Handling', function () {
    it('handles validation errors gracefully', function () {
        $response = $this->post(route('users.store'), [
            'name' => '', // Invalid: required field
            'email' => 'invalid-email', // Invalid: format
        ]);

        $response->assertSessionHasErrors(['name', 'email']);
    });

    it('handles database constraint violations', function () {
        User::factory()->create(['email' => '<EMAIL>']);

        $response = $this->post(route('users.store'), [
            'name' => 'New User',
            'email' => '<EMAIL>', // Duplicate
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ]);

        $response->assertSessionHasErrors(['email']);
    });
});
```

## Critical Testing Rules

### Mandatory Testing Requirements
- **ALWAYS** write tests for new features before implementation
- **ALWAYS** test both success and failure scenarios
- **ALWAYS** test validation rules and error handling
- **ALWAYS** test authorization and permissions
- **NEVER** commit code without corresponding tests
- **ALWAYS** maintain at least 80% code coverage

### Test Quality Standards
- **ALWAYS** use descriptive test names that explain what is being tested
- **ALWAYS** follow the AAA pattern: Arrange, Act, Assert
- **ALWAYS** test edge cases and boundary conditions
- **ALWAYS** use factories for test data generation
- **NEVER** rely on external services in unit tests
- **ALWAYS** clean up test data between tests

### Performance Testing
- **ALWAYS** test database query performance for large datasets
- **ALWAYS** verify N+1 query prevention
- **ALWAYS** test pagination performance
- **ALWAYS** measure response times for critical endpoints

## Testing Checklist

### Before Writing Tests
- [ ] Identify what needs to be tested (happy path, edge cases, errors)
- [ ] Create necessary factories and test data
- [ ] Set up proper test environment and dependencies
- [ ] Choose appropriate test type (unit, feature, integration)

### Test Implementation
- [ ] Write descriptive test names and documentation
- [ ] Follow AAA pattern (Arrange, Act, Assert)
- [ ] Test both success and failure scenarios
- [ ] Verify validation rules and error messages
- [ ] Test authorization and permission checks
- [ ] Clean up test data and mock external dependencies

### After Writing Tests
- [ ] Run tests and verify they pass
- [ ] Check test coverage meets requirements
- [ ] Review test quality and maintainability
- [ ] Ensure tests are fast and reliable
- [ ] Document complex test scenarios
