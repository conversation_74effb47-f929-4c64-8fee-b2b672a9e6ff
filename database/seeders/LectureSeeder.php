<?php

declare(strict_types=1);

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Lecture;
use App\Models\Campus;

class LectureSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get existing campuses
        $campuses = Campus::all();

        if ($campuses->isEmpty()) {
            $this->command->warn('No campuses found. Please seed campuses first.');
            return;
        }

        $lectures = [
            [
                'employee_id' => 'LEC001',
                'title' => 'Dr.',
                'first_name' => 'Sarah',
                'last_name' => 'Johnson',
                'email' => '<EMAIL>',
                'phone' => '+61 3 9214 8000',
                'mobile_phone' => '+61 400 123 456',
                'campus_id' => $campuses->first()->id,
                'department' => 'Computer Science',
                'faculty' => 'Science, Engineering and Technology',
                'specialization' => 'Software Engineering, Web Development',
                'expertise_areas' => ['Software Engineering', 'Web Development', 'Database Systems', 'Programming'],
                'academic_rank' => 'senior_lecturer',
                'highest_degree' => 'PhD',
                'degree_field' => 'Computer Science',
                'alma_mater' => 'University of Melbourne',
                'graduation_year' => 2015,
                'hire_date' => '2018-02-01',
                'employment_type' => 'full_time',
                'employment_status' => 'active',
                'preferred_teaching_days' => ['Monday', 'Wednesday', 'Friday'],
                'preferred_start_time' => '09:00',
                'preferred_end_time' => '17:00',
                'max_teaching_hours_per_week' => 20,
                'teaching_modalities' => ['in_person', 'online', 'hybrid'],
                'office_address' => 'Building A, Room 301',
                'office_phone' => '+61 3 9214 8301',
                'biography' => 'Dr. Sarah Johnson is a Senior Lecturer with expertise in software engineering and web development. She has published over 30 research papers and has industry experience with major tech companies.',
                'certifications' => ['AWS Certified Solutions Architect', 'Certified Scrum Master'],
                'languages' => ['English', 'French'],
                'salary' => 95000.00,
                'is_active' => true,
                'can_teach_online' => true,
                'is_available_for_assignment' => true,
            ],
            [
                'employee_id' => 'LEC002',
                'title' => 'Prof.',
                'first_name' => 'Michael',
                'last_name' => 'Chen',
                'email' => '<EMAIL>',
                'phone' => '+61 3 9214 8001',
                'mobile_phone' => '+61 400 234 567',
                'campus_id' => $campuses->first()->id,
                'department' => 'Information Technology',
                'faculty' => 'Science, Engineering and Technology',
                'specialization' => 'Artificial Intelligence, Machine Learning',
                'expertise_areas' => ['Artificial Intelligence', 'Machine Learning', 'Data Science', 'Python Programming'],
                'academic_rank' => 'professor',
                'highest_degree' => 'PhD',
                'degree_field' => 'Artificial Intelligence',
                'alma_mater' => 'Stanford University',
                'graduation_year' => 2010,
                'hire_date' => '2012-08-01',
                'employment_type' => 'full_time',
                'employment_status' => 'active',
                'preferred_teaching_days' => ['Tuesday', 'Thursday'],
                'preferred_start_time' => '10:00',
                'preferred_end_time' => '16:00',
                'max_teaching_hours_per_week' => 15,
                'teaching_modalities' => ['in_person', 'hybrid'],
                'office_address' => 'Building B, Room 205',
                'office_phone' => '+61 3 9214 8205',
                'biography' => 'Professor Michael Chen is a leading researcher in artificial intelligence and machine learning. He has received numerous awards and grants for his groundbreaking research.',
                'certifications' => ['Google Cloud Professional ML Engineer', 'Microsoft Azure AI Engineer'],
                'languages' => ['English', 'Mandarin', 'Cantonese'],
                'salary' => 135000.00,
                'is_active' => true,
                'can_teach_online' => true,
                'is_available_for_assignment' => true,
            ],
            [
                'employee_id' => 'LEC003',
                'title' => 'Ms.',
                'first_name' => 'Emma',
                'last_name' => 'Williams',
                'email' => '<EMAIL>',
                'phone' => '+61 3 9214 8002',
                'mobile_phone' => '+**************',
                'campus_id' => $campuses->count() > 1 ? $campuses->skip(1)->first()->id : $campuses->first()->id,
                'department' => 'Business Administration',
                'faculty' => 'Business and Law',
                'specialization' => 'Digital Marketing, E-commerce',
                'expertise_areas' => ['Digital Marketing', 'E-commerce', 'Social Media Strategy', 'Business Analytics'],
                'academic_rank' => 'lecturer',
                'highest_degree' => 'Master\'s',
                'degree_field' => 'Business Administration',
                'alma_mater' => 'Swinburne University of Technology',
                'graduation_year' => 2018,
                'hire_date' => '2020-03-01',
                'employment_type' => 'full_time',
                'employment_status' => 'active',
                'preferred_teaching_days' => ['Monday', 'Tuesday', 'Wednesday'],
                'preferred_start_time' => '08:00',
                'preferred_end_time' => '18:00',
                'max_teaching_hours_per_week' => 25,
                'teaching_modalities' => ['in_person', 'online'],
                'office_address' => 'Building C, Room 102',
                'office_phone' => '+61 3 9214 8102',
                'biography' => 'Emma Williams brings practical industry experience to her teaching, having worked in digital marketing for several leading companies before joining academia.',
                'certifications' => ['Google Ads Certified', 'Facebook Blueprint Certified', 'HubSpot Inbound Marketing'],
                'languages' => ['English', 'Spanish'],
                'salary' => 75000.00,
                'is_active' => true,
                'can_teach_online' => true,
                'is_available_for_assignment' => true,
            ],
            [
                'employee_id' => 'LEC004',
                'title' => 'Dr.',
                'first_name' => 'James',
                'last_name' => 'Anderson',
                'email' => '<EMAIL>',
                'phone' => '+61 3 9214 8003',
                'campus_id' => $campuses->first()->id,
                'department' => 'Engineering',
                'faculty' => 'Science, Engineering and Technology',
                'specialization' => 'Mechanical Engineering, Robotics',
                'expertise_areas' => ['Mechanical Engineering', 'Robotics', 'Automation', 'CAD Design'],
                'academic_rank' => 'associate_professor',
                'highest_degree' => 'PhD',
                'degree_field' => 'Mechanical Engineering',
                'alma_mater' => 'RMIT University',
                'graduation_year' => 2008,
                'hire_date' => '2014-01-15',
                'contract_start_date' => '2024-01-01',
                'contract_end_date' => '2026-12-31',
                'employment_type' => 'contract',
                'employment_status' => 'active',
                'preferred_teaching_days' => ['Thursday', 'Friday'],
                'preferred_start_time' => '09:00',
                'preferred_end_time' => '15:00',
                'max_teaching_hours_per_week' => 18,
                'teaching_modalities' => ['in_person'],
                'office_address' => 'Engineering Building, Room 405',
                'office_phone' => '+61 3 9214 8405',
                'emergency_contact_name' => 'Lisa Anderson',
                'emergency_contact_phone' => '+61 400 456 789',
                'emergency_contact_relationship' => 'Spouse',
                'biography' => 'Dr. James Anderson has extensive experience in mechanical engineering and robotics, with several patents and industry collaborations.',
                'certifications' => ['Professional Engineer (PE)', 'Certified SolidWorks Professional'],
                'languages' => ['English', 'German'],
                'hourly_rate' => 150.00,
                'is_active' => true,
                'can_teach_online' => false,
                'is_available_for_assignment' => true,
            ],
            [
                'employee_id' => 'LEC005',
                'title' => 'Dr.',
                'first_name' => 'Lisa',
                'last_name' => 'Thompson',
                'email' => '<EMAIL>',
                'phone' => '+61 3 9214 8004',
                'mobile_phone' => '+61 400 567 890',
                'campus_id' => $campuses->first()->id,
                'department' => 'Psychology',
                'faculty' => 'Health, Arts and Design',
                'specialization' => 'Clinical Psychology, Cognitive Behavioral Therapy',
                'expertise_areas' => ['Clinical Psychology', 'Cognitive Behavioral Therapy', 'Mental Health', 'Research Methods'],
                'academic_rank' => 'senior_lecturer',
                'highest_degree' => 'PhD',
                'degree_field' => 'Clinical Psychology',
                'alma_mater' => 'Monash University',
                'graduation_year' => 2012,
                'hire_date' => '2016-07-01',
                'employment_type' => 'part_time',
                'employment_status' => 'active',
                'preferred_teaching_days' => ['Monday', 'Wednesday'],
                'preferred_start_time' => '10:00',
                'preferred_end_time' => '14:00',
                'max_teaching_hours_per_week' => 12,
                'teaching_modalities' => ['in_person', 'online'],
                'office_address' => 'Health Sciences Building, Room 201',
                'office_phone' => '+61 3 9214 8201',
                'biography' => 'Dr. Lisa Thompson is a practicing clinical psychologist and researcher with expertise in cognitive behavioral therapy and mental health interventions.',
                'certifications' => ['Registered Psychologist (AHPRA)', 'CBT Specialist Certification'],
                'languages' => ['English', 'Italian'],
                'hourly_rate' => 120.00,
                'is_active' => true,
                'can_teach_online' => true,
                'is_available_for_assignment' => true,
                'notes' => 'Available for part-time teaching only due to clinical practice commitments.',
            ],
        ];

        foreach ($lectures as $lectureData) {
            Lecture::create($lectureData);
        }

        $this->command->info('Created ' . count($lectures) . ' sample lectures.');
    }
}
