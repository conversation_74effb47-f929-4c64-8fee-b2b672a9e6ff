<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Program;
use App\Models\Specialization;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ProgramSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing programs and specializations
        Specialization::query()->delete();
        Program::query()->delete();

        // Create Bachelor of Computer Science program (BCS)
        $program = Program::create([
            'name' => 'Bachelor of Computer Science',
            'code' => 'BCS',
            'description' => 'Comprehensive computer science program covering software development, artificial intelligence, and modern computing technologies.',
        ]);

        // Create Computer Science specialization (BCS-CS)
        Specialization::create([
            'program_id' => $program->id,
            'name' => 'Computer Science Major',
            'code' => 'BCS-CS',
            'description' => 'Specialization focusing on core computer science topics and advanced computing systems.',
            'is_active' => true,
        ]);
    }
}
