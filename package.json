{"private": true, "type": "module", "scripts": {"build": "vite build", "build:ssr": "vite build && vite build --ssr", "dev": "vite", "format": "prettier --write resources/", "format:check": "prettier --check resources/", "lint": "eslint . --fix", "setup": "./scripts/setup-project.sh", "validate": "./scripts/validate-setup.sh", "pre-push": "./scripts/pre-push.sh", "pre-push:docker": "./scripts/pre-push.sh --docker", "test:local": "./scripts/test-local.sh", "docker:up": "./scripts/docker-compose-dev.sh up", "docker:down": "./scripts/docker-compose-dev.sh down", "docker:logs": "./scripts/docker-compose-dev.sh logs", "docker:test": "docker-compose -f docker-compose.yml -f docker-compose.test.yml up -d", "docker:test:down": "docker-compose -f docker-compose.yml -f docker-compose.test.yml down -v", "type-check": "vue-tsc --noEmit"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/node": "^22.13.5", "@vue/eslint-config-typescript": "^14.3.0", "eslint": "^9.17.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-vue": "^9.32.0", "prettier": "^3.4.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "typescript-eslint": "^8.23.0", "vue-tsc": "^2.2.4"}, "dependencies": {"@inertiajs/vue3": "^2.0.0", "@tailwindcss/vite": "^4.1.1", "@tanstack/vue-table": "^8.21.3", "@vee-validate/zod": "^4.15.0", "@vitejs/plugin-vue": "^5.2.1", "@vueuse/core": "^12.8.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.0", "lucide-vue-next": "^0.468.0", "pinia": "^3.0.3", "reka-ui": "^2.3.1", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.1", "tw-animate-css": "^1.2.5", "typescript": "^5.2.2", "vee-validate": "^4.15.0", "vite": "^6.2.0", "vue": "^3.5.13", "vue-sonner": "^2.0.0", "ziggy-js": "^2.4.2", "zod": "^3.25.46"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.1"}}